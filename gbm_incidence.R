# <PERSON>
library(ggplot2)
library(dplyr)
library(optparse)


# Define plot theme
plot_theme <- function() {
  theme_cowplot() +
    theme(
      plot.title = element_text(hjust = 0.5, size = 14, face = "bold"),
      axis.title = element_text(size = 12),
      axis.text = element_text(size = 10),
      legend.title = element_text(size = 12),
      legend.text = element_text(size = 10)
    )
}

# Define command line options
option_list <- list(
  make_option(c("-i", "--input_file"), type="character", help="Path to input TSV file"),
  make_option(c("-o", "--figure_file"), type="character", help="Prefix for output files")
)

# Parse command line arguments
opt_parser <- OptionParser(option_list=option_list)
opt <- parse_args(opt_parser)


create_plot = function(){

# Create the main plot similar to amplicon p3
p = ggplot(oncogene_data, aes(x = reorder(sample_label, canonical_oncogenes_count), y = canonical_oncogenes_count)) + 
geom_bar(stat = "identity", color = "black") +
geom_text(aes(label = canonical_oncogenes), hjust = -0.1, size = 3, angle = 0) +
    
labs(title = "Canonical Oncogenes per ecDNA", x = "ecDNA (Sample_circ_ID)", y = "Count of Canonical Oncogenes") +

plot_theme() +
theme(axis.text.x = element_text(angle = 90, hjust = 1, vjust = 0.5))


print(p)

return()
}


pdf(opt$figure_file)

# Read the input data
raw_data <- read_tsv(opt$input_file, show_col_types = FALSE)



dev.off()

